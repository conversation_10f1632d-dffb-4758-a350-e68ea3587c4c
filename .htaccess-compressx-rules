# CompressX.io Apache Rules
# Add these rules to your .htaccess file if you're using Apache instead of Nginx

# BEGIN CompressX
<IfModule mod_rewrite.c>
RewriteEngine On

# Check if browser supports AVIF
RewriteCond %{HTTP_ACCEPT} image/avif
RewriteCond %{REQUEST_FILENAME} \.(jpe?g|png|gif|webp)$
RewriteCond %{REQUEST_FILENAME}\.avif -f
RewriteRule ^(.+)\.(jpe?g|png|gif|webp)$ $1.$2.avif [T=image/avif,E=avif:1,L]

# Check if browser supports WebP
RewriteCond %{HTTP_ACCEPT} image/webp
RewriteCond %{REQUEST_FILENAME} \.(jpe?g|png|gif)$
RewriteCond %{REQUEST_FILENAME}\.webp -f
RewriteRule ^(.+)\.(jpe?g|png|gif)$ $1.$2.webp [T=image/webp,E=webp:1,L]
</IfModule>

# Set Vary header for images
<IfModule mod_headers.c>
<FilesMatch "\.(jpe?g|png|gif|webp|avif)$">
Header append Vary Accept
</FilesMatch>
</IfModule>

# Set cache headers for images
<IfModule mod_expires.c>
ExpiresActive On
ExpiresByType image/jpeg "access plus 1 year"
ExpiresByType image/png "access plus 1 year"
ExpiresByType image/gif "access plus 1 year"
ExpiresByType image/webp "access plus 1 year"
ExpiresByType image/avif "access plus 1 year"
</IfModule>
# END CompressX
