<?php
/**
 * Must-Use Plugin: Live Site Error Suppression
 * 
 * Upload this file to: /wp-content/mu-plugins/error-suppression-live.php
 * on your live server to suppress WordPress notices and warnings.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Force error suppression on live site
if (!defined('WP_DEBUG')) {
    define('WP_DEBUG', false);
}

if (!defined('WP_DEBUG_DISPLAY')) {
    define('WP_DEBUG_DISPLAY', false);
}

if (!defined('WP_DEBUG_LOG')) {
    define('WP_DEBUG_LOG', true);
}

// Suppress all notices and warnings
error_reporting(E_ERROR | E_PARSE | E_CORE_ERROR | E_COMPILE_ERROR);
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
ini_set('log_errors', 1);

// Start output buffering to catch any early output
if (!ob_get_level()) {
    ob_start();
}

// Custom error handler for WordPress-specific issues
function limitless_live_error_handler($errno, $errstr, $errfile, $errline) {
    // Suppress translation loading warnings
    if (strpos($errstr, '_load_textdomain_just_in_time was called incorrectly') !== false) {
        return true;
    }
    
    // Suppress header modification warnings
    if (strpos($errstr, 'Cannot modify header information') !== false) {
        return true;
    }
    
    // Suppress ACF warnings
    if (strpos($errstr, 'acf domain was triggered too early') !== false) {
        return true;
    }
    
    // Suppress Limit Login Attempts warnings
    if (strpos($errstr, 'limit-login-attempts-reloaded domain was triggered too early') !== false) {
        return true;
    }
    
    // Log other errors but don't display them
    if ($errno & (E_WARNING | E_NOTICE)) {
        error_log("WordPress Notice/Warning: $errstr in $errfile on line $errline");
        return true;
    }
    
    return false;
}

// Set the error handler
set_error_handler('limitless_live_error_handler', E_ALL);

// Clean output buffer and suppress notices in output
add_action('init', function() {
    if (ob_get_level()) {
        $content = ob_get_clean();
        // Remove any notice/warning output
        $clean_content = preg_replace('/^(Notice|Warning|Fatal error):.*$/m', '', $content);
        $clean_content = preg_replace('/\s*in \/.*? on line \d+\s*/', '', $clean_content);
        echo $clean_content;
        ob_start();
    }
}, 1);

// Prevent translation loading too early
add_filter('override_load_textdomain', function($override, $domain, $mofile) {
    $problematic_domains = ['acf', 'limit-login-attempts-reloaded'];
    
    if (in_array($domain, $problematic_domains) && !did_action('init')) {
        return true; // Prevent early loading
    }
    
    return $override;
}, 10, 3);

// Force proper translation loading timing
add_action('init', function() {
    // Reload ACF translations at proper time
    if (function_exists('load_plugin_textdomain')) {
        load_plugin_textdomain('acf');
        load_plugin_textdomain('limit-login-attempts-reloaded');
    }
}, 5);

// Final cleanup before page output
add_action('wp_loaded', function() {
    restore_error_handler();
    
    // One final output buffer clean
    if (ob_get_level()) {
        $content = ob_get_clean();
        $clean_content = preg_replace('/^(Notice|Warning):.*$/m', '', $content);
        echo $clean_content;
        ob_start();
    }
}, 999);

// Shutdown function to ensure clean output
register_shutdown_function(function() {
    while (ob_get_level()) {
        $content = ob_get_clean();
        // Only output content that doesn't contain error messages
        if (!preg_match('/^(Notice|Warning|Fatal error):/m', $content)) {
            echo $content;
        }
    }
});
