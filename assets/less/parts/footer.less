@import '../vw_values.less';
@import '../constants.less';

body {
  &.touch {
    
  }
}

.footer {
    font-size: @vw18;
    background: @primaryColor;
    padding: @vw70 0 @vw20 0;
    color: @hardWhite;
    position: relative;
    overflow: hidden;
    &:after {
      z-index: 1;
    }
    a {
      color: @hardWhite;
      text-decoration: none;
      .transitionMore(opacity, .3s);
    }
    .footerTitle {
      text-transform: uppercase;
      font-size: @vw20;
      color: rgba(255, 255, 255, 0.4);
      margin-bottom: @vw20;
      font-family: 'ApexMk2-Regular', Arial, sans-serif;
      i {
        color: @secondaryColorLight;
      }
    }
    p {
      font-size: @vw18;
    }
    &.inview {
      .divider {
        .transform(scaleX(1));
        .transitionMore(transform, .6s, .45s, cubic-bezier(0.85, 0, 0.15, 1));
      }
    }
    .artists {
      margin-bottom: -@vw2;
    }
    .artist {
      margin-bottom: @vw2;
      font-family: 'ApexMk2-Regular', <PERSON><PERSON>, sans-serif;
      text-transform: uppercase !important;
      .transitionMore(opacity, .3s);
      line-height: 1.5;
      .innerText {
        cursor: pointer;
      }
      &:hover {
          opacity: .5;
      }
    }
    .backgroundImage {
      position: absolute;
      top: 0%;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 0;
      .transform(translate3d(0,0,0));
      -webkit-mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1), rgba(0,0,0,0));
      mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1), rgba(0,0,0,0));
      &:before {
        height: 50%;
        position: absolute;
        top: 0;
        width: 100%;
        left: 0;
        pointer-events: none;
        z-index: 2;
        content: '';
        background: (linear-gradient(0deg,rgba(8,0,54,0), @primaryColor));
      }
      &:after {
        height: 50%;
        position: absolute;
        top: auto;
        bottom: 0;
        width: 100%;
        left: 0;
        pointer-events: none;
        z-index: 2;
        content: '';
        background: (linear-gradient(180deg,rgba(8,0,54,0), @primaryColor));
      }
      img {
        position: absolute;
        top: 50%;
        left: 50%;
        .transform(translate(-50%,-50%) scale(1));
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
    .socials {
      margin-top: @vw40;
      .social {
        display: inline-block;
        height: @vw46;
        width: @vw46;
        cursor: pointer;
        .rounded(50%);
        background: rgba(255,255,255,.1);
        text-decoration: none;
        color: @hardWhite;
        line-height: @vw49;
        -webkit-transition: color .3s, background-color .3s;
        transition: color .3s, background-color .3s;
        text-align: center;
        font-size: @vw22;
        &:not(:last-child) {
          margin-right: @vw20;
        }
        &:hover {
          background-color: rgba(255,255,255, 1);
          color: @primaryColor;
        }
        i {
          pointer-events: none;
        }
      }
    }
    .logo {
      .transitionMore(opacity, .3s);
      &:hover {
        opacity: .5;
      }
      img {
        display: inline-block;
        height: @vw30;
        width: auto;
        margin-bottom: @vw70;
        object-fit: contain;
      }
    }
    ul {
      line-height: 1.5;
      list-style: none;
      li {
        a {
          color: @hardWhite;
          font-size: @vw18;
          text-decoration: none;
          cursor: pointer;
          .transitionMore(color, .3s);
          &:hover {
            color: @secondaryColorLight;
          }
        }
      }
    }
    .link {
      color: @secondaryColorLight;
      font-size: @vw18;
      display: inline-block;
      text-transform: lowercase;
      text-decoration: none;
      line-height: 1.4;
      cursor: pointer;
      position: relative;
      &:hover {
        &:before, &:after {
          .transitionMore(transform, .3s, 0s, cubic-bezier(0.87, 0, 0.13, 1));
        }
        &:before {
          .transform(scaleX(0));
          transition-delay: 0s;
        }
        &:after {
          .transform(scaleX(1));
          transition-delay: .15s;
        }
      }
      &:before, &:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: auto;
        transform-origin: right;
        right: 0;
        background: @secondaryColorLight;
        .transform(scaleX(1));
        height: 1px;
        width: 100%;
      }
      &:after {
        left: 0;
        right: auto;
        transform-origin: left;
        .transform(scaleX(0));
      }
      &:not(:last-child) {
        margin-right: @vw5;
      }
    }
    .topFooter {
      .cols {
        .col {
          &:first-child {
            width: calc(50% ~"-" @vw16);
          }
        }
      }
    }
    .footerContent {
      position: relative;
    }
    .cols {
      display: flex;
      flex-wrap: wrap;
        .col {
          display: inline-block;
          vertical-align: top;
          margin: 0 @vw8;
          padding-right: @vw40;
          width: calc(25% ~"-" @vw16);
        }
      }
    .bottomFooter {
      display: flex;
      flex-wrap: wrap;
        color: @hardBlack;
        margin-top: @vw22;
        .col {
          display: inline-block;
          vertical-align: middle;
          width: 50%;
          &:last-child {
            text-align: right;
          }
        }
        .menu {
          opacity: .7;
          li {
            display: inline-block;
          }
          a {
              display: inline-block;
              vertical-align: middle;
              padding: @vw10;
              cursor: pointer;
              color: @hardWhite;
              text-decoration: none;
              .transitionMore(opacity, .3s);
              &:not(:last-of-type) {
                  margin-right: @vw22;
              }
              &:hover {
                color: @hardWhite;
                opacity: .5;
              }
          }
    }
    .logo {
      width: @vw24;
      display: inline-block;
      vertical-align: middle;
      svg {
        object-fit: contain;
        width: 100%;
        height: auto;
      }
    }
  }
  .divider {
    margin: @vw44 0;
    .transform(scaleX(0));
    transform-origin: left;
    height: 1px;
    background: @secondaryColorLight;
  }
  .middleFooter {
    .cols {
      .col {
        padding-right: @vw40;
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .footer {
    font-size: @vw18-1080;
    padding: @vw100-1080 0 @vw20-1080 0;
    .logo {
      img {
        height: @vw30-1080;
        margin-bottom: @vw70-1080;
      }
    }
    p {
      font-size: @vw18-1080;
    }
    .link {
      font-size: @vw18-1080;
    }
    .footerTitle {
      font-size: @vw20-1080;
    }
    .socials {
      margin-top: @vw40-1080;
      .social {
        height: @vw46-1080;
        width: @vw46-1080;
        line-height: @vw49-1080;
        font-size: @vw22-1080;
        &:not(:last-child) {
          margin-right: @vw20-1080;
        }
      }
    }
    .artists {
      margin-bottom: -@vw2-1080;
      .artist {
        margin-bottom: @vw2-1080;
      }
    }
    .cols {
      margin-left: -@vw20-1080;
      width: calc(100% ~"+" @vw40-1080);
      .col {
        margin: 0 @vw20-1080;
        width: calc(25% ~"-" @vw40-1080);
      }
    }
    .topFooter {
      .cols {
        .col {
          &:first-child {
            width: calc(50% ~"-" @vw40-1080);
          }
        }
      }
    }
    .cols {
      .col {
        padding-right: @vw20-1080;
        width: calc(25% ~"-" @vw40-1080);
      }
    }
    ul {
      li {
        a {
          font-size: @vw18-1080;
        }
      }
    }
    .divider {
      margin: @vw44-1080 0;
    }
    .bottomFooter {
      .menu {
        a {
          padding: @vw10-1080;
          &:not(:last-of-type) {
            margin-right: @vw22-1080;
          }
        }
      }
    }
  }
}
  
@media all and (max-width: 580px) {
  .footer {
    font-size: @vw24-580;
    padding: @vw100-580 0 @vw20-580 0;
    .logo {
      img {
        height: @vw40-580;
        margin-bottom: @vw60-580;
      }
    }
    p {
      font-size: @vw24-580;
    }
    .link {
      font-size: @vw24-580;
    }
    .footerTitle {
      font-size: @vw20-580;
    }
    .socials {
      margin-top: @vw40-580;
      .social {
        height: @vw55-580;
        width: @vw55-580;
        line-height: @vw55-580;
        font-size: @vw22-580;
        &:not(:last-child) {
          margin-right: @vw20-580;
        }
      }
    }
    .artists {
      margin-bottom: -@vw5-580;
      .artist {
        margin-bottom: @vw5-580;
        &:not(:last-child) {
          margin-right: @vw5-580;
        }
      }
    }
    .cols {
      margin-left: -@vw20-580;
      width: calc(100% ~"+" @vw40-580);
      .col {
        margin: 0 @vw20-580;
        width: calc(50% ~"-" @vw40-580);
      }
    }
    .topFooter {
      .cols {
        .col {
          &:first-child {
            width: calc(100% ~"-" @vw40-580);
            margin-bottom: @vw50-580;
          }
        }
      }
    }
    .cols {
      .col {
        padding-right: @vw20-580;
        width: calc(50% ~"-" @vw40-580);
      }
    }
    ul {
      li {
        a {
          font-size: @vw24-580;
        }
      }
    }
    .divider {
      margin: @vw44-580 0;
    }
    .bottomFooter {
      .menu {
        a {
          padding: @vw10-580;
          &:not(:last-of-type) {
            margin-right: @vw22-580;
          }
        }
      }
    }
  }
}