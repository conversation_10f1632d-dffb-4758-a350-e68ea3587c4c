$(document).ready(function(){
  $(document).on("initPage", function () {
    setHeaderBackground();
  });
});

function setHeaderBackground() {
  // Clear any existing ScrollTriggers for header background
  ScrollTrigger.getAll().forEach(trigger => {
    if (trigger.vars && trigger.vars.id === 'headerBackground') {
      trigger.kill();
    }
  });

  const $header = $("header");
  const $sections = $("section");

  // Check if first section has whiteBackground class on page load
  const $firstSection = $sections.first();
  if ($firstSection.hasClass('whiteBackground')) {
    $header.addClass('showBackground');
  } else {
    $header.removeClass('showBackground');
  }

  // Create ScrollTrigger for each section to monitor which one is in view
  $sections.each(function(index) {
    const section = this;
    const $section = $(section);

    ScrollTrigger.create({
      id: 'headerBackground',
      trigger: section,
      start: "top center",
      end: "bottom center",
      onEnter: () => {
        if (index === 0 && !$section.hasClass('whiteBackground')) {
          // First section without whiteBackground = no background initially
          $header.removeClass('showBackground');
        } else {
          // Any other case = show background
          $header.addClass('showBackground');
        }
      },
      onEnterBack: () => {
        if (index === 0 && !$section.hasClass('whiteBackground')) {
          // First section without whiteBackground = no background initially
          $header.removeClass('showBackground');
        } else {
          // Any other case = show background
          $header.addClass('showBackground');
        }
      },
      onLeave: () => {
        // Once scrolled away from first section, always show background
        $header.addClass('showBackground');
      },
      onLeaveBack: () => {
        if (index === 0 && !$section.hasClass('whiteBackground')) {
          // Back to first section without whiteBackground = no background
          $header.removeClass('showBackground');
        } else {
          // Any other case = show background
          $header.addClass('showBackground');
        }
      }
    });
  });
}
