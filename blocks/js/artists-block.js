$(document).ready(function(){
    $(document).on("initPage", function () {
      if ($(".artistsBlock").length > 0) {
          initializeArtists();
      }
    });
});

function getUrlParam(name) {
  var results = new RegExp('[?&]' + name + '=([^&#]*)').exec(window.location.search);
  return results ? decodeURIComponent(results[1]) : null;
}

function updateArtistsUrlParam(type) {
  if (history.replaceState) {
    var url = new URL(window.location.href);
    url.searchParams.set('artists', type);
    history.replaceState(null, '', url.toString());
  }
}

function showArtistsFilter(type, animate = true) {
  console.log('showArtistsFilter called with type:', type);
  var $block = $('.artistsBlock[data-init]');
  if (!$block.length) return;

  var $filters = $block.find('.filters .filter');
  var $artists = $block.find('.artists .artist');
  var $intros = $block.find('.artists .intro');

  $filters.removeClass('active');
  $filters.filter('[data-filter="' + type + '"]').addClass('active');
  $intros.hide();
  $intros.filter('[data-artist-type="' + type + '"]').show();
  var $toShow = $artists.filter('[data-artist-type="' + type + '"]');
  $artists.hide();

  if (animate && $toShow.length) {
    gsap.set($toShow, {opacity: 0, y: 40, display: 'block'});
    gsap.to($toShow, {
      opacity: 1,
      y: 0,
      stagger: 0.07,
      duration: 0.6,
      ease: 'power3.out',
      onStart: function() { $toShow.show(); }
    });
  } else {
    $toShow.show();
  }
  updateArtistsUrlParam(type);
}

function setupArtistsLayout() {
  var $block = $('.artistsBlock[data-init]');
  if (!$block.length) return;

  // Zorg dat partials naast elkaar staan (flex) maar laat CSS de responsive breedte bepalen
  $block.find('.artists').css({
    display: 'flex',
    flexWrap: 'wrap',
    alignItems: 'flex-start',
    justifyContent: 'flex-start'
  });

  // Verwijder eventuele hardcoded flex/width styles en laat CSS de responsive breedte bepalen
  $block.find('.partial').css({
    flex: 'none', // Laat CSS de breedte bepalen via width property
    maxWidth: 'none',
    minWidth: '0',
    margin: '', // Reset margin, laat CSS dit bepalen
    boxSizing: 'border-box',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start'
  });
}

function applyInitialArtistsFilter() {
  var param = getUrlParam('artists');
  if (param === 'ones_to_watch' || param === 'main') {
    showArtistsFilter(param, false);
  } else {
    showArtistsFilter('main', false);
  }
}

function initializeArtists() {
  var $block = $('.artistsBlock[data-init]');
  if (!$block.length) return;

  setupArtistsLayout();

  // Setup click handlers with namespaced events for Swup compatibility
  // Use document delegation to ensure handlers work after page transitions
  $(document).off('click.artistsFilter', '.artistsBlock[data-init] .filters .filter')
             .on('click.artistsFilter', '.artistsBlock[data-init] .filters .filter', function() {
    console.log('Filter clicked:', $(this).data('filter'));
    var type = $(this).data('filter');
    showArtistsFilter(type);
  });

  // Apply initial filter
  applyInitialArtistsFilter();
}
