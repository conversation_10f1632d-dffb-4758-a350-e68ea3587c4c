$(document).ready(function(){
  $(document).on("initPage", function () {
    if ($(".bigHeaderBlock").length > 0){
        setTimeout(function(){
            initializeBigHeaderBlock();
        }, 450);
    }
  });
});

function initializeBigHeaderBlock() {
  const iframe = document.querySelector(".bigHeaderBlock iframe");

  if (!iframe) return;

  // Voeg ID toe aan iframe (indien nog niet aanwezig)
  iframe.id = "youtube-player";

  // YouTube API script toevoegen (eenmalig)
  if (typeof YT === "undefined" || typeof YT.Player === "undefined") {
    const tag = document.createElement("script");
    tag.src = "https://www.youtube.com/iframe_api";
    const firstScriptTag = document.getElementsByTagName("script")[0];
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
  }

  // Als API klaar is, video afspelen
  window.onYouTubeIframeAPIReady = function () {
    new YT.Player("youtube-player", {
      events: {
        onReady: function (event) {
          event.target.playVideo();
        }
      }
    });
  };
}
