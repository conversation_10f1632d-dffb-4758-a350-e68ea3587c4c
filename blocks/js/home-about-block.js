$(document).ready(function(){
    $(document).on("initPage", function () {
      if ($(".homeAboutBlock").length > 0){
        initHomeAboutBlock();
      }
  });
});

function initHomeAboutBlock() {
  if ($(window).outerWidth() <= 580) {
    $(".homeAboutBlock .imageRows").remove();
    return;
  }
  ScrollTrigger.create({
    trigger: ".homeAboutBlock",
    start: "top top",
    end: "bottom bottom",
    pin: ".homeAboutBlock .stickyItem",
    pinSpacing: true,
  });

  if ($(".homeAboutBlock .stickyItem video").length > 0) {
    gsap.to(".homeAboutBlock .stickyItem video", .6, {
      scale: 1,
      scrollTrigger: {
        trigger: ".homeAboutBlock",
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });
  } else {
    gsap.to(".homeAboutBlock .stickyItem img", .6, {
      scale: 1,
      scrollTrigger: {
        trigger: ".homeAboutBlock",
        start: "top bottom",
        end: "bottom top",
        scrub: true
      }
    });
  }

  // Parallax images
  var parallaxImages = [
    { sel: '.imageRow:nth-of-type(2) .imageWrapper:nth-of-type(1)', y: 40 },
    { sel: '.imageRow:nth-of-type(2) .imageWrapper:nth-of-type(2)', y: 120 },
    
    { sel: '.imageRow:nth-of-type(3) .imageWrapper:nth-of-type(1)', y: 50 },
    { sel: '.imageRow:nth-of-type(3) .imageWrapper:nth-of-type(2)', y: 30 },
    
    { sel: '.imageRow:nth-of-type(4) .imageWrapper:nth-of-type(1)', y: 100 },
    { sel: '.imageRow:nth-of-type(4) .imageWrapper:nth-of-type(2)', y: 0 },
    { sel: '.imageRow:nth-of-type(4) .imageWrapper:nth-of-type(3)', y: 180 },
    
    { sel: '.imageRow:nth-of-type(5) .imageWrapper:nth-of-type(1)', y: 60 },
    { sel: '.imageRow:nth-of-type(5) .imageWrapper:nth-of-type(2)', y: 10 },
    
    { sel: '.imageRow:nth-of-type(6) .imageWrapper:nth-of-type(1)', y: 40 },
    { sel: '.imageRow:nth-of-type(6) .imageWrapper:nth-of-type(2)', y: 160 },
    
    { sel: '.imageRow:nth-of-type(7) .imageWrapper:nth-of-type(1)', y: 80 },
    { sel: '.imageRow:nth-of-type(7) .imageWrapper:nth-of-type(2)', y: 20 },
    { sel: '.imageRow:nth-of-type(7) .imageWrapper:nth-of-type(3)', y: 60 },
    
    { sel: '.imageRow:nth-of-type(8) .imageWrapper:nth-of-type(1)', y: 60 },
    { sel: '.imageRow:nth-of-type(8) .imageWrapper:nth-of-type(2)', y: 30 },
    { sel: '.imageRow:nth-of-type(8) .imageWrapper:nth-of-type(3)', y: 100 },
  ];

  parallaxImages.forEach(function(img, i) {
    if($(img.sel).length) {
      gsap.to(img.sel, {
        y: -img.y * 20,
        ease: "none",
        scrollTrigger: {
          trigger: ".homeAboutBlock",
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });
    }
  });
}