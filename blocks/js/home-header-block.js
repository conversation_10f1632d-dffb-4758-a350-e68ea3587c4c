var autoSlide;
$(document).ready(function(){
  $(document).on("initPage", function () {
      if (autoSlide) autoSlide.kill();
      if ($(".homeHeaderBlock").length > 0){
        initializeHomeHeaderBlock();
      }

      if ($("body.touch").length > 0) {
        $(document).on("touchend", ".homeHeaderBlock .button", function(event) {
            event.preventDefault();
            let link = $(this).attr("href");
            if (link && typeof pageContainerWrap !== "undefined") {
              pageContainerWrap.loadPage({ url: link });
            } else {
            }
        });
    }
  });
});

function initializeHomeHeaderBlock() {
  var currentIndex = 0;
  var slides = $(".homeHeaderBlock .artistSlider .slide");
  var totalSlides = slides.length;
  var direction = "next";
  var isAnimating = false;

  var heighestTitleSlide = 0;
  $(".homeHeaderBlock .titleSlide").each(function (index, slide) {
    var titleHeight = $(slide).outerHeight();
    if (titleHeight > heighestTitleSlide) {
      heighestTitleSlide = titleHeight;
    }
  });
  $(".homeHeaderBlock .titleSlider").css("height", heighestTitleSlide);

  var indicatorTl;

  function startAutoSlide() {
    if (autoSlide) autoSlide.kill();
    if (indicatorTl) indicatorTl.kill();
    updateSliderIndicator(currentIndex, true);
    autoSlide = gsap.timeline({ repeat: -1 });
    autoSlide.to({}, { duration: 5, onComplete: function () {
      nextSlide();
    }});
  }

  function nextSlide() {
    if (isAnimating) return;
    isAnimating = true;
    var prevIndex = currentIndex;
    currentIndex = (currentIndex + 1) % totalSlides;
    showSlide(currentIndex, false);
    isAnimating = false;
    startAutoSlide();
  }

  function prevSlide() {
    if (isAnimating) return;
    isAnimating = true;
    var prevIndex = currentIndex;
    currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
    showSlide(currentIndex, false);
    isAnimating = false;
    startAutoSlide();
  }

  function clearAllFilters() {
    slides.each(function() {
      var $imgTop = $(this).find('.imageSlide.top img');
      var $imgBottom = $(this).find('.imageSlide.bottom img');
      $imgTop.css('filter', '');
      $imgBottom.css('filter', '');
    });
  }

  function updateSliderIndicator(index, animate = true) {
    var total = slides.length;
    var current = index + 1;
    $(".sliderIndicatorCount .currentSlide").text(current);
    $(".sliderIndicatorCount .totalSlides").text(total);
    var $bar = $(".sliderIndicatorProgress");
    $bar.css({ transformOrigin: 'left center' });
    if (indicatorTl) indicatorTl.kill();
    gsap.set($bar, { scaleX: 0 });
    if (animate !== false) {
      indicatorTl = gsap.to($bar, { scaleX: 1, duration: 5, ease: 'linear' });
    }
  }

  function startAutoSlide() {
    if (autoSlide) autoSlide.kill();
    if (indicatorTl) indicatorTl.kill();
    updateSliderIndicator(currentIndex, true);
    autoSlide = gsap.timeline({ repeat: -1 });
    autoSlide.to({}, { duration: 5, onComplete: function () {
      nextSlide();
    }});
  }

  function showSlide(index, first=false) {
    slides.removeClass('active');
    slides.eq(index).addClass('active');
    $(".homeHeaderBlock .titleSlide").removeClass("active");
    $(".homeHeaderBlock .titleSlide").eq(index).addClass("active");
    updateSliderIndicator(index, !first);
    // Reset en start indicator altijd opnieuw
    if (indicatorTl) indicatorTl.kill();
    var $bar = $(".sliderIndicatorProgress");
    gsap.set($bar, { scaleX: 0 });
    indicatorTl = gsap.to($bar, { scaleX: 1, duration: 5, ease: 'linear' });
  }

  function animateSplitSlide(fromIdx, toIdx, onComplete) {
    var $fromSlide = slides.eq(fromIdx);
    var $top = $fromSlide.find('.imageSlide.top');
    var $bottom = $fromSlide.find('.imageSlide.bottom');
    var $toSlide = slides.eq(toIdx);
    gsap.to($top, { x: '-50', duration: 0.5, ease: 'power2.in' });
    gsap.to($bottom, { x: '50', duration: 0.5, ease: 'power2.in', onComplete: function() {
      // Distort/flicker effect na uit-animatie
      var flickerTl = gsap.timeline({onComplete: function() {
        // Na flicker, fade out en dan pas nieuwe slide in
        gsap.to([$top, $bottom], { opacity: 0, duration: 0.18, onComplete: function() {
          $fromSlide.removeClass('active');
          slides.eq(toIdx).addClass('active');
          // Reset posities en opacity
          gsap.set($top, { x: 0, opacity: 1 });
          gsap.set($bottom, { x: 0, opacity: 1 });
          // updateSliderIndicator(toIdx); // deze regel is nu overbodig want showSlide doet het
          if (typeof onComplete === 'function') onComplete();
        }});
      }});
      flickerTl.to([$top, $bottom], { opacity: 0, duration: 0.07 })
        .to([$top, $bottom], { opacity: 1, duration: 0.07 })
        .to([$top, $bottom], { opacity: 0, duration: 0.07 })
        .to([$top, $bottom], { opacity: 1, duration: 0.07 })
        .to([$top, $bottom], { opacity: 0.7, duration: 0.07 })
        .to([$top, $bottom], { opacity: 1, duration: 0.07 });
    }});
  }

  function showSlide(index, first=false) {
    slides.removeClass('active');
    slides.eq(index).addClass('active');
    $(".homeHeaderBlock .titleSlide").removeClass("active");
    $(".homeHeaderBlock .titleSlide").eq(index).addClass("active");
    updateSliderIndicator(index, !first);
    // Reset en start indicator altijd opnieuw
    if (indicatorTl) indicatorTl.kill();
    var $bar = $(".sliderIndicatorProgress");
    gsap.set($bar, { scaleX: 0 });
    indicatorTl = gsap.to($bar, { scaleX: 1, duration: 5, ease: 'linear' });
  }

  $(".homeHeaderBlock .next").click(function () {
    nextSlide();
  });

  $(".homeHeaderBlock .prev").click(function () {
    prevSlide();
  });

  showSlide(currentIndex, true);
  startAutoSlide();
}