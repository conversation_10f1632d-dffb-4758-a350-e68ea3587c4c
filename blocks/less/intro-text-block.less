// out: false

@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.introTextBlock {
  .col {
    display: inline-block;
    vertical-align: top;
    width: calc(100% ~"- (" (@vw106 * 2) + (@vw16 * 2) ~")");
    &.small {
      width: (@vw106 * 2) + (@vw16 * 2);
    }
  }
  .introTextWrapper {
    position: relative;
    display: block;
    .mediumTitle {
      &:not(.overlayText) {
        opacity: .4;
      }
      &.overlayText {
        position: absolute;
        pointer-events: none;
        top: 0;
        left: 0;
        .line {
          width: 0%;
          white-space: nowrap;
        }
      }
    }
  }
  .text {
    margin-top: @vw60;
    padding-left: @vw106 + @vw16;
    padding-right: (@vw106 * 3) + (@vw16 * 3);
    .icon {
      width: @vw30;
      display: inline-block;
      vertical-align: top;
      font-size: @vw20;
      line-height: 2;
      .filter(blur(0px));
      .transitionMore(filter,.3s);
      &:hover {
        .filter(blur(10px));
      }
    }
    .innerText {
      display: inline-block;
      vertical-align: top;
      width: calc(100% ~"-" @vw30);
      padding-left: @vw40;
    }
  }
}

@media all and (max-width: 1080px) {
  .introTextBlock {
    .text {
      margin-top: @vw60-1080;
      padding-left: @vw106-1080 + @vw16-1080;
      padding-right: (@vw106-1080 * 2) + (@vw16-1080 * 2);
      .icon {
        width: @vw30-1080;
        font-size: @vw20-1080;
      }
      .innerText {
        width: calc(100% ~"-" @vw30-1080);
        padding-left: @vw40-1080;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .introTextBlock {
    .col {
      width: 100%;
      display: block;
      &:not(:first-child) {
        margin-top: @vw30-580;
      }
      &.small {
        width: 100%;
      }
    }
    .introTextWrapper {
      .mediumTitle {
        &.overlayText {
          .line {
            width: 0%;
          }
        }
      }
    }
    .text {
      margin-top: @vw60-580;
      padding-left: 0;
      padding-right: 0;
      .transform(translateY(@vw30-580));
      .icon {
        width: @vw30-580;
        font-size: @vw20-580;
        line-height: @vw30-580;
      }
      .innerText {
        width: calc(100% ~"-" @vw30-580);
        padding-left: @vw40-580;
        p {
          &:not(:last-child) {
            margin-bottom: @vw22-580;
          }
        }
      }
    }
  }
}