// out: false
@import '../../assets/less/constants.less';

.textSliderBlock {
  width: 100%;
  overflow: hidden;
  position: relative;
  .slider { 
    left: 0;
    top: 0;
    white-space: nowrap;
    width: 100%;
    transform: translateX(0);
    .innerSlider {
      position: relative;
    }
    .slide {
      margin-right: @vw20;
      width: auto;
      display: inline-block;
      white-space: normal;
      .hugeTitle {
        display: block;
        margin-right: @vw20;
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .textSliderBlock {
    .slider {
      .slide {
        margin-right: @vw20-1080;
        .hugeTitle {
          margin-right: @vw20-1080;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .textSliderBlock {
    .slider { 
      .slide {
        margin-right: @vw20-580;
        .hugeTitle {
          margin-right: @vw20-580;
        }
      }
    }
  }
}
