// out: false

@import '../../assets/less/vw_values.less';
@import '../../assets/less/constants.less';

.textBlock {
  &.inview {
    .text {
      p {
        opacity: 1;
        .transform(translateY(0));
        transition: opacity 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1), transform 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1);
        .stagger(40, 0.15s, .75s);
      
      }
    }
    .buttonWrapper {
      .textLink {
        opacity: 1;
        transition: opacity 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1), transform 0.45s .75s cubic-bezier(0, 0.55, 0.45, 1);
        .transform(translateY(0));
        .stagger(20, 0.05s);
      }
    }
  }
  .text {
    p {
      .transform(translateY(@vw30));
      opacity: 0;
      a {
        color: @secondaryColor;
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }
  &.center {
    text-align: center;
  }
  &.right {
    text-align: right;
  }
  .buttonWrapper {
    margin-bottom: @vw50;
    .textLink {
      display: table;
      margin: auto;
      opacity: 0;
      .transform(translateY(@vw30));
      &:not(:last-child) {
        margin-bottom: @vw22;
      }
    }
  }
}

@media all and (max-width: 1080px) {
  .textBlock {
    .text {
      p {
        .transform(translateY(@vw30-1080));
      }
    }
    .buttonWrapper {
      margin-bottom: @vw50-1080;
      .textLink {
        .transform(translateY(@vw30-1080));
        &:not(:last-child) {
          margin-bottom: @vw22-1080;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .textBlock {
    .text {
      p {
        .transform(translateY(@vw30-580));
      }
    }
    .buttonWrapper {
      margin-bottom: @vw50-580;
      .textLink {
        .transform(translateY(@vw30-580));
        &:not(:last-child) {
          margin-bottom: @vw22-580;
        }
      }
    }
  }
}