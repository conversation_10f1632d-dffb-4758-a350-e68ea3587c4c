<section class="contactBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="cols">
        <div class="col" data-parallax data-parallax-speed="-1">
            <div class="images">
                <div class="imageWrapper">
                    <div class="innerImage">
                        <?php $image1 = get_field('image_1'); ?>
                        <?php if ($image1): ?>
                            <img class="lazy" data-src="<?php echo esc_url($image1['sizes']['large']); ?>" alt="<?php echo esc_attr($image1['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
                <div class="imageWrapper">
                    <div class="innerImage">
                        <?php $image2 = get_field('image_2'); ?>
                        <?php if ($image2): ?>
                            <img class="lazy" data-src="<?php echo esc_url($image2['sizes']['large']); ?>" alt="<?php echo esc_attr($image2['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col">
            <div class="formWrapper">
                <div class="formIntro">
                    <h2 class="biggerTitle"><?php the_field("title"); ?></h2>
                    <div class="text"><p><?php the_field('text'); ?></p></div>
                </div>
                <?php echo(the_field("form")); ?>
            </div>
        </div>
        
        <div class="col" data-parallax data-parallax-speed="-1">
            <div class="images">
                <div class="imageWrapper">
                    <div class="innerImage">
                        <?php $image3 = get_field('image_3'); ?>
                        <?php if ($image3): ?>
                            <img class="lazy" data-src="<?php echo esc_url($image3['sizes']['large']); ?>" alt="<?php echo esc_attr($image3['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
                <div class="imageWrapper">
                    <div class="innerImage">
                        <?php $image4 = get_field('image_4'); ?>
                        <?php if ($image4): ?>
                            <img class="lazy" data-src="<?php echo esc_url($image4['sizes']['large']); ?>" alt="<?php echo esc_attr($image4['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
