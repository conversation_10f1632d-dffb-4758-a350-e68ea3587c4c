<section class="projectsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor"); ?>"<?php endif; ?>>
  <div class="contentWrapper">
    <div class="cols">
      <?php
      // Haal de ACF-categorieën op
      $acf_categories = get_field('project_categories');
      $category_slugs = [];
      
      if ($acf_categories) {
          foreach ($acf_categories as $cat) {
              $category_slugs[] = $cat->slug;
          }
      }
      
      $query_args = [
        'posts_per_page' => -1,
        'order' => 'DESC',
        'orderby' => 'date',
        'post_type' => 'project',
      ];
      
      // Voeg filtering toe als er ACF-categorieën zijn
      if (!empty($category_slugs)) {
          $query_args['tax_query'] = [
              [
                  'taxonomy' => 'category',
                  'field' => 'slug',
                  'terms' => $category_slugs,
              ]
          ];
      }
      
      $allProjects = new WP_Query($query_args);
      
      $index = 0;
      while ($allProjects->have_posts()) : $allProjects->the_post();
        $index++;
        $description = get_field('description');
        $title = get_the_title();
        $image = get_field('image', get_the_ID());
        $link = get_permalink();
        $text = get_field('text', get_the_ID());
        $categories = get_the_category();
        $category_names = array_map(function($cat) { return $cat->name; }, $categories);
        if ($index == 7): $index = 3; endif;
      ?>
        <div class="col" data-parallax data-parallax-speed="<?php echo $index; ?>">
          <?php if ($index === 2 && $description): ?>
            <span class="text bigger white" data-lines data-words><?php echo esc_html($description); ?></span>
            <span class="buttonWrapper">
              <?php render_text_link('button'); ?>
            </span>
          <?php endif; ?>
          <a href="<?php echo esc_url($link); ?>" title="<?php echo esc_html($title); ?>" class="project">
            <?php if ($image): ?>
              <span class="imageWrapper">
                <span class="innerImage">
                  <img class="lazy" data-src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>">
                </span>
              </span>
              <span class="innerCols">
                <span class="innerCol">
                  <h2 class="tinyTitle"><?php echo esc_html($title); ?></h2>
                  <?php if (count($category_names) > 0): ?>
                    <span class="divider">|</span>
                    <span class="category"><p><?php echo esc_html(implode(', ', $category_names)); ?></p></span>
                  <?php endif; ?>
                </span>
                <span class="innerCol">
                  <span class="textLink">
                    <span class="innerText"><?php the_field("label_text"); ?></span>
                    <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
                  </span>
                </span>
              </span>
            <?php endif; ?>
            </a>
        </div>
      <?php
      endwhile;
      wp_reset_postdata();
      ?>
    </div>
  </div>
</section>
