<?php
$size = 'full'; 
$video = get_field("video");
$image = get_field("image");
?>
<section class="twoImageTextBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper small">
        <h2 class="biggerTitle white"><?php the_field("title"); ?></h2>
        <div class="cols">
            <div class="col">
                <div class="imageWrapper">
                    <div class="innerImage">
                        <?php 
                        $video1 = get_field('video_1');
                        $image1 = get_field('image_1'); 
                        ?>
                        <?php if ($video1): ?>
                            <video poster="<?php echo esc_url($image1['url']); ?>" class="video" muted playsinline loop autoplay>
                                <source src="<?php echo esc_url($video1); ?>" type="video/mp4">
                            </video>
                        <?php elseif ($image1): ?>
                            <img class="lazy" data-src="<?php echo esc_url($image1['sizes']['large']); ?>" alt="<?php echo esc_attr($image1['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
                <div class="innerCols">
                    <div class="innerCol">
                        <h3 class="subTitle"><?php the_field('title_1'); ?></h3>
                    </div>
                    <div class="innerCol">
                        <div class="text white"><p><?php the_field('text_1'); ?></p></div>
                    </div>
                </div>
            </div>

            <div class="col">
                <div class="imageWrapper">
                    <div class="innerImage">
                        <?php 
                        $video2 = get_field('video_2');
                        $image2 = get_field('image_2'); 
                        ?>
                        <?php if ($video2): ?>
                            <video poster="<?php echo esc_url($image2['url']); ?>" class="video" muted playsinline loop autoplay>
                                <source src="<?php echo esc_url($video2); ?>" type="video/mp4">
                            </video>
                        <?php elseif ($image2): ?>
                            <img class="lazy" data-src="<?php echo esc_url($image2['sizes']['large']); ?>" alt="<?php echo esc_attr($image2['alt']); ?>" />
                        <?php endif; ?>
                    </div>
                </div>
                <div class="innerCols">
                    <div class="innerCol">
                        <h3 class="subTitle"><?php the_field('title_2'); ?></h3>
                    </div>
                    <div class="innerCol">
                        <div class="text white"><p><?php the_field('text_2'); ?></p></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
