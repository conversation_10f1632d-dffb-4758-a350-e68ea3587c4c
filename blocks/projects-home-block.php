<section class="projectsHomeBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor"); ?>"<?php endif; ?>>
  <div class="contentWrapper">
    <div class="subTitle primary"><?php the_field("subtitle"); ?></div>
    <h2 class="biggerTitle white"><?php the_field("title"); ?></h2>
  </div>
  <div class="contentWrapper">
    <div class="cols">
      <?php
      $projects = get_field("projects");
      if ($projects) :
        $index = 0;
        foreach ($projects as $project) :
          $index++;
          $post_id = $project->ID;
          $title = get_the_title($post_id);
          $image = get_field('image', $post_id);
          $link = get_permalink($post_id);
          $description = get_field("text");
          $categories = get_the_category($post_id);
          $category_names = array_map(function($cat) { return $cat->name; }, $categories);
        ?>
          <div class="col" data-parallax data-parallax-speed="<?php echo $index; ?>">
            <?php if ($index === 2 && $description): ?>
              <div class="text bigger white" data-lines data-words><?php echo esc_html($description); ?></div>
              <div class="buttonWrapper">
                <?php render_text_link('button'); ?>
              </div>
            <?php endif; ?>
            <a href="<?php echo esc_url($link); ?>" title="<?php echo esc_html($title); ?>" class="project">
              <?php if ($image): ?>
                <span class="imageWrapper">
                  <span class="innerImage">
                    <img class="lazy" data-src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>">
                  </span>
                </span>
                <span class="innerCols">
                  <span class="innerCol">
                    <h2 class="tinyTitle"><?php echo esc_html($title); ?></h2>
                    <?php if (count($category_names) > 0): ?>
                      <span class="divider">|</span>
                      <span class="category"><p><?php echo esc_html(implode(', ', $category_names)); ?></p></span>
                    <?php endif; ?>
                  </span>
                  <span class="innerCol">
                    <span class="textLink">
                      <span class="innerText"><?php the_field("label_text"); ?></span>
                      <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
                    </span>
                  </span>
                </span>
              <?php endif; ?>
            </a>
          </div>
      <?php
        endforeach;
      endif;
      ?>
    </div>
  </div>
</section>
