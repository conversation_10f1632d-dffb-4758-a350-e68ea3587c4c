<?php
$size = 'full'; 
$images = get_field('images'); // Haal de galerij op
$text = get_field('text');
$anchor = get_field('anchor');
$uid = uniqid('', true);
?>

<section class="projectInfoBlock" data-init <?php if ($anchor): ?>data-anchor="<?php echo esc_attr($anchor); ?>"<?php endif; ?>>
  <div class="contentWrapper">
    <?php if ($images): ?>
      <div class="cols">
        <div class="col">
          <?php if (!empty($images[0])): ?>
            <div class="imageWrapper" data-index="0" data-gallery-id="<?php echo($uid); ?>" data-gallery data-image="<?php echo esc_url($images[0]['url']); ?>" >
              <img data-init class="lazy" data-src="<?php echo esc_url($images[0]['url']); ?>" alt="<?php echo esc_attr($images[0]['alt']); ?>">
            </div>
          <?php endif; ?>
        </div>
        <div class="col empty"></div>
        <div class="col">
          <span class="backgroundWrapper"><span class="background" data-parallax data-parallax-speed="4"></span></span>
          <div class="text bigger white" data-lines data-words><?php the_field("text") ?></div>
        </div>
      </div>

      <div class="cols">
        <?php 
        $imageCount = count($images);
        for ($i = 1; $i < $imageCount; $i++) {
          $colIndex = ($i - 1) % 6;
          $index = $i;
          if ($colIndex == 0 || $colIndex == 5) {
            echo '<div class="col"></div>
              <div class="col">
                    <div class="imageWrapper" data-index="'.($index).'" data-gallery-id="'.$uid.'" data-gallery data-image="' . esc_url($images[$i]['url']) . '">
                      <img data-init class="lazy" data-src="' . esc_url($images[$i]['url']) . '" alt="' . esc_attr($images[$i]['alt']) . '">
                    </div>
                  </div>'
            ;
          } else {
            echo '<div class="col">
                    <div class="imageWrapper" data-index="'.($index ).'" data-gallery-id="'.$uid.'" data-gallery data-image="' . esc_url($images[$i]['url']) . '">
                      <img data-init class="lazy" data-src="' . esc_url($images[$i]['url']) . '" alt="' . esc_attr($images[$i]['alt']) . '">
                    </div>
                  </div>';
          }
        }
        ?>
      </div>
    <?php endif; ?>
    </div>
</section>
<div class="overlay" style="display: none;" data-gallery-id="<?php echo($uid); ?>">
  <span class="background"></span>
  <span class="close">&times;</span>
  <div class="overlayContent">
    <div class="overlayImage">
    </div>
  </div>
  <div class="navButton prev"><i class="icon-arrow-left"></i></div>
  <div class="navButton next"><i class="icon-arrow-right"></i></div>
</div>