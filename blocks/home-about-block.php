<?php
$background_image = get_field('background_image');
$background_video = get_field('background_video');
$size = "medium_large";
?>
<section class="homeAboutBlock noMarginBottom" data-hide-cursor data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
   <div class="stickyItem">
      <?php if ($background_video): ?>
        <video class="video isStickyVideo" muted playsinline loop autoplay poster="<?php echo esc_url($background_image["sizes"]['medium_large']); ?>">
            <source src="<?php echo esc_url($background_video); ?>" type="video/mp4">
        </video>
      <?php elseif ($background_image): ?>
        <img class="lazy" data-src="<?php echo esc_url($background_image["sizes"]['large']); ?>" alt="<?php echo esc_attr($background_image['alt']); ?>" />
      <?php endif; ?>
      <div class="innerContent">
        <h2 class="bigTitle white"><?php the_field("title") ?></h2>
        <div class="subTitle white"><?php the_field("subtitle") ?></div>
        <div class="socials">
          <?php if(get_theme_mod('customTheme-main-callout-instagram')): ?>
            <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-instagram', 'https://www.instagram.com')); ?>" title="instagram" target="_blank"><i class="icon-instagram"></i></a>
          <?php endif; ?>
          <?php if(get_theme_mod('customTheme-main-callout-tiktok')): ?>
            <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-tiktok', 'https://www.tiktok.com')); ?>" title="tiktok" target="_blank"><i class="icon-tiktok"></i></a>
          <?php endif; ?>
          <?php if(get_theme_mod('customTheme-main-callout-linkedin')): ?>
            <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-linkedin', 'https://www.linkedin.com')); ?>" title="linkedin" target="_blank"><i class="icon-linkedin"></i></a>
          <?php endif; ?>
          <?php if(get_theme_mod('customTheme-main-callout-facebook')): ?>
            <a class="social" href="<?php echo esc_url(get_theme_mod('customTheme-main-callout-facebook', 'https://www.facebook.com')); ?>" title="facebook" target="_blank"><i class="icon-facebook"></i></a>
          <?php endif; ?>
        </div>
      </div>
   </div>
   <?php
    $gallery_images = get_field('images');
    $size = 'medium_large'; // of 'large', 'full', etc.
    $images_per_row = [3, 2, 2, 3, 2, 2, 3, 2]; // Defineer hier hoeveel afbeeldingen per rij
    $image_index = 0;

    if ($gallery_images):
    ?>
    <div class="imageRows">
      <?php foreach ($images_per_row as $row_count): ?>
        <div class="imageRow">
          <?php for ($i = 0; $i < $row_count; $i++): ?>
            <?php if (isset($gallery_images[$image_index])): 
              $img = $gallery_images[$image_index];
            ?>
              <div class="imageWrapper">
                <div class="innerImage">
                  <img class="lazy" data-src="<?php echo esc_url($img['sizes'][$size]); ?>" alt="<?php echo esc_attr($img['alt']); ?>" />
                </div>
              </div>
            <?php endif; $image_index++; ?>
          <?php endfor; ?>
        </div>
      <?php endforeach; ?>
    </div>
    <?php endif; ?>

  </div>
</section>
