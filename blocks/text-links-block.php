<section class="textBlock <?php the_field("position"); ?>" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor") ?>"<?php endif; ?>>
    <div class="contentWrapper">
    <div class="buttonWrapper">
    <?php 
    $socials = [
        [
            'url' => 'tel:' . get_theme_mod('customTheme-main-callout-telephone'),
            'title' => get_theme_mod('customTheme-main-callout-telephone-label'),
            'icon' => 'icon-phone'
        ],
        [
            'url' => 'mailto:' . get_theme_mod('customTheme-main-callout-mail'),
            'title' => get_theme_mod('customTheme-main-callout-mail'),
            'icon' => 'icon-mail'
        ],
        [
            'url' => esc_url(get_theme_mod('customTheme-main-callout-instagram', 'https://www.instagram.com')),
            'title' => 'Instagram',
            'icon' => 'icon-insta'
        ],
        [
            'url' => esc_url(get_theme_mod('customTheme-main-callout-tiktok', 'https://www.tiktok.com')),
            'title' => 'TikTok',
            'icon' => 'icon-tiktok'
        ]
    ];
    
    foreach ($socials as $social) {
        $link_url = $social['url'];
        $link_title = $social['title'];
        $link_icon = $social['icon'];
        $link_target = '_blank';
    ?>
    <a href="<?php echo esc_url($link_url); ?>" title="<?php echo esc_html($link_title); ?>" class="textLink bigger" target="<?php echo esc_attr($link_target); ?>">
        <span class="innerText">
            <?php echo esc_html($link_title); ?>
        </span>
        <span class="arrows">
            <i class="icon-arrow-right-up"></i>
            <i class="social <?php echo esc_attr($link_icon); ?>"></i>
        </span>
    </a>
    <?php } ?>
</div>

        <div class="text white" data-lines data-words><?php the_field("text"); ?></div>
    </div>
</section>
