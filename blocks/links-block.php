<section class="projectsBlock" data-init <?php if (get_field("anchor")): ?>data-anchor="<?php the_field("anchor"); ?>"<?php endif; ?>>
  <div class="contentWrapper">
    <?php $total_items = count(get_field('projecten')); $extra_class = ($total_items == 2) ? ' twoItems' : ''; ?>
    <div class="cols<?php echo $extra_class; ?>">
    <?php
      if (have_rows('projecten')):
        while (have_rows('projecten')) : the_row();
          $title = get_sub_field('titel');
          $image = get_sub_field('afbeelding');
          $link = get_sub_field('link');
          $label_text = get_sub_field('label_text');
      ?>
        <div class="col" data-parallax data-parallax-speed="<?php echo esc_attr($index); ?>">
          <a href="<?php echo esc_url($link); ?>" title="<?php echo esc_html($title); ?>" class="project">
            <?php if ($image): ?>
              <span class="imageWrapper">
                <span class="innerImage">
                  <img class="lazy" data-src="<?php echo esc_url($image['url']); ?>" alt="<?php echo esc_attr($image['alt']); ?>">
                </span>
              </span>
              <span class="innerCols">
                <span class="innerCol">
                  <h2 class="tinyTitle"><?php echo esc_html($title); ?></h2>
                </span>
                <span class="innerCol">
                  <span class="textLink">
                    <span class="innerText"><?php echo esc_html($label_text); ?></span>
                    <span class="arrows"><i class="icon-arrow-right-up"></i><i class="icon-arrow-right-up"></i></span>
                  </span>
                </span>
              </span>
            <?php endif; ?>
          </a>
        </div>
      <?php
        endwhile;
      endif;
      ?>
    </div>
  </div>
</section>
