//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';
.artistLatestReleasblock {
  .cols {
    width: calc(100% ~"+" @vw16);
    margin-left: -@vw8;
    .col {
      display: inline-block;
      margin: 0 @vw8;
      width: calc(50% ~"-" @vw16);
      vertical-align: top;
      &:first-child {
        padding-right: @vw106 + @vw16;
      }
      img {
        width: 100%;
        height: auto;
      }
    }
  }
  .smallTitle {
    margin-bottom: @vw22;
  }
  .button {
    margin-top: @vw55;
  }
}

@media all and (max-width: 1080px) {
  .artistLatestReleasblock {
    .cols {
      width: calc(100% ~"+" @vw16-1080);
      margin-left: -@vw8-1080;
      .col {
        margin: 0 @vw8-1080;
        width: calc(50% ~"-" @vw16-1080);
        &:first-child {
          padding-right: @vw106-1080 + @vw16-1080;
        }
      }
    }
    .smallTitle {
      margin-bottom: @vw22-1080;
    }
    .button {
      margin-top: @vw55-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .artistLatestReleasblock {
    .cols {
      width: 100%;
      margin-left: 0;
      .col {
        margin: 0;
        width: 100%;
        display: block;
        &:first-child {
          padding-right: 0;
          margin-bottom: @vw30-580;
        }
      }
    }
    .smallTitle {
      margin-bottom: @vw22-580;
    }
    .button {
      margin-top: @vw55-580;
    }
  }
}