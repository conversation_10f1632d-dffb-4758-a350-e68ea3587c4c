//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.artistBigSliderBlock {
  .slide {
    width: 100%;
    margin: 0 @vw8;
    height: auto;
    position: relative;
  }
  .imageWrapper {
    width: 100%;
    height: auto;
    position: relative;
    overflow: hidden;
    .innerImage {
      height: 0;
      .paddingRatio(1204,568);
    }
    img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
  }
  .sliderButton {
    position: absolute;
    top: 50%;
    .transform(translateY(-50%));
    &.prev {
      left: 0;
    }
    &.next {
      right: 0;
    }
  }
}

@media all and (max-width: 1080px) {
  .artistBigSliderBlock {
    .slide {
      margin: 0 @vw8-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .artistBigSliderBlock {
    .slide {
      margin: 0 @vw8-580;
      .imageWrapper {
        .innerImage {
          .paddingRatio(3, 4);
        }
      }
    }
  }
}
