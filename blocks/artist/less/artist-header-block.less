//out: false
@import '../../../assets/less/vw_values.less';
@import '../../../assets/less/constants.less';

.artistHeaderBlock {
  padding-top: (@vw100 * 3);
  &.inview {
    .artistLogo {
      .transform(scale(1));
      .transitionMore(transform, .6s, .15s, cubic-bezier(0.34, 1.56, 0.64, 1));
    }
    .socials {
      .social {
        .transform(translateY(0));
        opacity: 1;
        transition: opacity 0.3s 0.75s ease-out, transform 0.3s 0.75s ease-out;
        .stagger(20, 0.05s);
      }
    }
  }
  .artistLogo {
    display: block;
    margin: auto;
    text-align: center;
    width: auto;
    max-height: @vw90 * 1.5;
    max-width: @vw100 * 3;
    object-fit: contain;
    margin-bottom: @vw50;
    .transform(scale(0));
  }
  .ArtistGenre {
    color: #fff;
    font-size: (@vw100 * 1.1);
    letter-spacing: 2px;
    margin-bottom: (@vw100 * 0.5);
  }
  .hugeTitle {
    text-align: center;
  }
  .socials {
    margin-top: @vw50;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: @vw60;
    a {
      cursor: pointer;
      color: @hardWhite;
      text-decoration: none;
      font-size: @vw25;
      .transform(translateY(@vw20));
      opacity: 0;
      i {
        cursor: pointer;
        .transitionMore(color, .3s);
      }
      &:hover {
        i {
          color: @secondaryColorLight;
        }
      }
    }
  }
  .backToArtists {
    margin: @vw72 0 @vw22 0;
  }
  .infoWrapper {
    display: flex;
    flex-direction: row;
    min-height: @vw100 * 5;
    position: relative;
    .contactDetails {
      position: relative;
      right: 0;
      top: 0;
      height: auto;
      width: (@vw106 * 5) + (@vw16 * 4);
      background: @primaryColor;
      padding: @vw44 @vw33;
      border: 1px solid @secondaryColorLight;
    }
    .text {
      margin-bottom: @vw40;
    }
    .button {
      width: 100%;
      &:not(:last-child) {
        margin-bottom: @vw10;
      }
    }
  }
  .imageWrapper, .backgroundImage {
    width: 100%;
    height: auto;
    overflow: hidden;
    .innerImage {
      height: 100%;
    }
    img {
        position: absolute;
        top: -10%;
        left: 0;
        width: 100%;
        height: 120%;
        object-fit: cover;
        object-position: top;
      }
  }
  .backgroundImage {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 70%;
    overflow: hidden;
    z-index: 0;
    .transform(translate3d(0,0,0));
    -webkit-mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1), rgba(0,0,0,0));
    mask-image: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,1), rgba(0,0,0,0));
    img {
      .filter(blur(20px));
    }
  }
}

@media all and (max-width: 1080px) {
  .artistHeaderBlock {
    padding-top: (@vw100-1080 * 3);
    .backToArtists {
      margin: @vw72-1080 0 @vw22-1080 0;
    }
    .artistLogo {
      max-height: @vw90-1080 * 1.7;
      max-width: @vw100-1080 * 3;
      margin-bottom: @vw50-1080;
    }
    .ArtistGenre {
      font-size: (@vw100-1080 * 1.1);
      margin-bottom: (@vw100-1080 * 0.5);
    }
    .socials {
      margin-top: @vw50-1080;
      .social {
        height: @vw46-1080;
        width: @vw46-1080;
        line-height: @vw49-1080;
        font-size: @vw22-1080;
        &:not(:last-child) {
          margin-right: @vw20-1080;
        }
      }
    }
    .button {
      margin-top: @vw30-1080;
    }
  }
}

@media all and (max-width: 580px) {
  .artistHeaderBlock {
    padding-top: @vw120-580 !important;
    .backToArtists {
      margin: @vw72-580 0 @vw22-580 0;
    }
    .hugeTitle {
      font-size: @vw70-580;
    }
    .artistLogo {
      max-height: @vw60-580 * 1.7;
      max-width: @vw100-580 * 1.7;
      margin-bottom: @vw20-580;
    }
    .ArtistGenre {
      font-size: (@vw100-580 * 1.1);
      margin-bottom: (@vw100-580 * 0.5);
    }
    .imageWrapper {
      height: auto;
      .innerImage {
        .paddingRatio(1,0.5);
        height: 0;
        width: 100%;
        img {
          .transform(translate3d(0,0,0)) !important;
        }
      }
    }
    .infoWrapper {
      flex-direction: column;
      .contactDetails {
        width: 100%;
        padding: @vw33-580 @vw22-580;
        height: auto;
      }
    }
    .socials {
      margin-top: @vw50-580;
      .social {
        height: @vw46-580;
        width: @vw46-580; 
        line-height: @vw49-580;
        font-size: @vw22-580;
        &:not(:last-child) {
          margin-right: @vw20-580;
        }
      }
    }
    .button {
      margin-top: @vw22-580;
    }
  }
}
