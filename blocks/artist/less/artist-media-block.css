.artistMediaBlock {
  padding: 4.63vw 0;
  background: transparent;
  position: relative;
}
.artistMediaBlock.inview .mediaContainer .youtubeContainer,
.artistMediaBlock.inview .mediaContainer .spotifyContainer {
  opacity: 1;
  -webkit-transform: translateY(0);
  -moz-transform: translateY(0);
  -o-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
  transition: opacity 0.6s 0.2s ease-out, transform 0.6s 0.2s ease-out;
}
.artistMediaBlock.inview .mediaContainer .youtubeContainer:nth-child(2),
.artistMediaBlock.inview .mediaContainer .spotifyContainer:nth-child(2) {
  transition-delay: 0.3s;
}
.artistMediaBlock.inview .mediaContainer .youtubeContainer:nth-child(1),
.artistMediaBlock.inview .mediaContainer .spotifyContainer:nth-child(1) {
  transition-delay: 0.15s;
}
.artistMediaBlock .normalTitle {
  text-align: left;
  margin-bottom: 1.505vw;
}
.artistMediaBlock .mediaContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2.315vw;
  align-items: flex-start;
}
.artistMediaBlock .youtubeContainer,
.artistMediaBlock .spotifyContainer {
  opacity: 0;
  -webkit-transform: translateY(2.894vw);
  -moz-transform: translateY(2.894vw);
  -o-transform: translateY(2.894vw);
  -ms-transform: translateY(2.894vw);
  transform: translateY(2.894vw);
}
.artistMediaBlock .youtubeContainer .videoWrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  overflow: hidden;
}
.artistMediaBlock .youtubeContainer .videoWrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
.artistMediaBlock .spotifyContainer {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  overflow: hidden;
}
.artistMediaBlock .spotifyContainer iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
.artistMediaBlock .noMediaMessage {
  text-align: center;
  padding: 2.315vw;
}
.artistMediaBlock .noMediaMessage p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.579vw;
}
.artistMediaBlock .noMediaMessage p:last-child {
  margin-bottom: 0;
}
.artistMediaBlock .noMediaMessage p small {
  font-size: 0.81vw;
  opacity: 0.8;
}
.artistMediaBlock .spotify-fallback,
.artistMediaBlock .youtube-fallback {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
  background: rgba(255, 255, 255, 0.05);
  -webkit-border-radius: 0.868vw;
  -moz-border-radius: 0.868vw;
  border-radius: 0.868vw;
}
.artistMediaBlock .spotify-fallback .fallback-message,
.artistMediaBlock .youtube-fallback .fallback-message {
  text-align: center;
}
.artistMediaBlock .spotify-fallback .fallback-message p,
.artistMediaBlock .youtube-fallback .fallback-message p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.868vw;
  font-size: 0.926vw;
}
.artistMediaBlock .spotify-fallback .fallback-message p:last-of-type,
.artistMediaBlock .youtube-fallback .fallback-message p:last-of-type {
  margin-bottom: 1.157vw;
}
.artistMediaBlock .spotify-fallback .fallback-message p small,
.artistMediaBlock .youtube-fallback .fallback-message p small {
  font-size: 0.81vw;
  opacity: 0.8;
}
.artistMediaBlock .spotify-fallback .fallback-message .button,
.artistMediaBlock .youtube-fallback .fallback-message .button {
  display: inline-flex;
  align-items: center;
  gap: 0.579vw;
}
@media all and (max-width: 1080px) {
  .artistMediaBlock {
    padding: 7.407vw 0;
  }
  .artistMediaBlock .normalTitle {
    margin-bottom: 2.407vw;
  }
  .artistMediaBlock .mediaContainer {
    gap: 3.704vw;
  }
  .artistMediaBlock .youtubeContainer,
  .artistMediaBlock .spotifyContainer {
    -webkit-transform: translateY(4.63vw);
    -moz-transform: translateY(4.63vw);
    -o-transform: translateY(4.63vw);
    -ms-transform: translateY(4.63vw);
    transform: translateY(4.63vw);
  }
  .artistMediaBlock .noMediaMessage {
    padding: 3.704vw;
  }
  .artistMediaBlock .noMediaMessage p {
    margin-bottom: 0.926vw;
  }
  .artistMediaBlock .noMediaMessage p small {
    font-size: 1.296vw;
  }
  .artistMediaBlock .spotify-fallback,
  .artistMediaBlock .youtube-fallback {
    -webkit-border-radius: 1.389vw;
    -moz-border-radius: 1.389vw;
    border-radius: 1.389vw;
  }
  .artistMediaBlock .spotify-fallback .fallback-message p,
  .artistMediaBlock .youtube-fallback .fallback-message p {
    margin-bottom: 1.389vw;
    font-size: 1.481vw;
  }
  .artistMediaBlock .spotify-fallback .fallback-message p:last-of-type,
  .artistMediaBlock .youtube-fallback .fallback-message p:last-of-type {
    margin-bottom: 1.852vw;
  }
  .artistMediaBlock .spotify-fallback .fallback-message p small,
  .artistMediaBlock .youtube-fallback .fallback-message p small {
    font-size: 1.296vw;
  }
  .artistMediaBlock .spotify-fallback .fallback-message .button,
  .artistMediaBlock .youtube-fallback .fallback-message .button {
    gap: 0.926vw;
  }
}
@media all and (max-width: 580px) {
  .artistMediaBlock {
    padding: 13.793vw 0;
  }
  .artistMediaBlock .normalTitle {
    margin-bottom: 4.482vw;
  }
  .artistMediaBlock .mediaContainer {
    grid-template-columns: 1fr;
    gap: 5.172vw;
  }
  .artistMediaBlock .youtubeContainer,
  .artistMediaBlock .spotifyContainer {
    -webkit-transform: translateY(8.62vw);
    -moz-transform: translateY(8.62vw);
    -o-transform: translateY(8.62vw);
    -ms-transform: translateY(8.62vw);
    transform: translateY(8.62vw);
  }
  .artistMediaBlock .noMediaMessage {
    padding: 6.897vw;
  }
  .artistMediaBlock .noMediaMessage p {
    margin-bottom: 1.724vw;
  }
  .artistMediaBlock .noMediaMessage p small {
    font-size: 2.414vw;
  }
  .artistMediaBlock .spotify-fallback,
  .artistMediaBlock .youtube-fallback {
    -webkit-border-radius: 2.586vw;
    -moz-border-radius: 2.586vw;
    border-radius: 2.586vw;
  }
  .artistMediaBlock .spotify-fallback .fallback-message p,
  .artistMediaBlock .youtube-fallback .fallback-message p {
    margin-bottom: 2.586vw;
    font-size: 2.758vw;
  }
  .artistMediaBlock .spotify-fallback .fallback-message p:last-of-type,
  .artistMediaBlock .youtube-fallback .fallback-message p:last-of-type {
    margin-bottom: 3.448vw;
  }
  .artistMediaBlock .spotify-fallback .fallback-message p small,
  .artistMediaBlock .youtube-fallback .fallback-message p small {
    font-size: 2.414vw;
  }
  .artistMediaBlock .spotify-fallback .fallback-message .button,
  .artistMediaBlock .youtube-fallback .fallback-message .button {
    gap: 1.724vw;
  }
}
