<?php
$artist_id = $args['artist_id'] ?? get_the_ID();
$desc = get_field('description', $artist_id);

if ($desc):
    // Haal alle alinea’s eruit (gescheiden door dubbele enter of <p> tags)
    $paragraphs = preg_split('/\s*<\/p>\s*/i', $desc, -1, PREG_SPLIT_NO_EMPTY);

    // Herstel <p> tags
    $paragraphs = array_map(function($p) {
        return '<p>' . trim(strip_tags($p, '<a><strong><em><br>')) . '</p>';
    }, $paragraphs);

    $total = count($paragraphs);
    $half = ceil($total / 2);
    $col1 = array_slice($paragraphs, 0, $half);
    $col2 = array_slice($paragraphs, $half);
?>
<section class="artistTwoColumnTextBlock" data-init data-show-cursor>
    <div class="contentWrapper smaller">
        <div class="cols">
            <div class="col">
                <?= implode("\n", $col1) ?>
            </div>
            <div class="col">
                <?= implode("\n", $col2) ?>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>
