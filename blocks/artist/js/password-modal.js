$(document).ready(function(){
    $(document).on("initPage", function () {
        if ($(".password-protected-download").length > 0) {
            initializePasswordProtection();
        }
    });
});

function initializePasswordProtection() {
    // Handle password protected download buttons
    $('.password-protected-download').on('click', function(e) {
        e.preventDefault();
        
        const $button = $(this);
        const url = $button.data('url');
        const password = $button.data('password');
        const type = $button.data('type');
        
        // If no password is set, go directly to the URL
        if (!password || password.trim() === '') {
            window.open(url, '_blank');
            return;
        }
        
        // Show password modal
        showPasswordModal(url, password, type);
    });
    
    // Handle modal close
    $('#passwordModalClose, .password-modal-overlay').on('click', function() {
        hidePasswordModal();
    });
    
    // Handle password submission
    $('#passwordModalApply').on('click', function() {
        submitPassword();
    });
    
    // Handle Enter key in password input
    $('#passwordInput').on('keypress', function(e) {
        if (e.which === 13) { // Enter key
            submitPassword();
        }
    });
    
    // Handle Escape key
    $(document).on('keydown', function(e) {
        if (e.which === 27 && $('#passwordModal').is(':visible')) { // Escape key
            hidePasswordModal();
        }
    });
}

function showPasswordModal(url, password, type) {
    const $modal = $('#passwordModal');
    const $title = $('#passwordModalTitle');
    const $input = $('#passwordInput');
    const $error = $('#passwordError');
    
    // Set modal title based on type
    const titles = {
        'presskit': 'PRESSKIT — PASSWORD',
        'visual_pack': 'VISUAL PACK — PASSWORD'
    };
    
    $title.text(titles[type] || 'DOWNLOAD — PASSWORD');
    
    // Store data for submission
    $modal.data('url', url);
    $modal.data('password', password);
    
    // Reset form
    $input.val('');
    $error.hide();
    
    // Show modal with animation
    $modal.show();
    setTimeout(() => {
        $modal.addClass('show');
        $input.focus();
    }, 10);
    
    // Prevent body scroll
    $('body').addClass('modal-open');
}

function hidePasswordModal() {
    const $modal = $('#passwordModal');
    
    $modal.removeClass('show');
    setTimeout(() => {
        $modal.hide();
    }, 300);
    
    // Restore body scroll
    $('body').removeClass('modal-open');
}

function submitPassword() {
    const $modal = $('#passwordModal');
    const $input = $('#passwordInput');
    const $error = $('#passwordError');
    const $applyButton = $('#passwordModalApply');
    
    const enteredPassword = $input.val().trim();
    const correctPassword = $modal.data('password');
    const url = $modal.data('url');
    
    // Show loading state
    $applyButton.text('CHECKING...');
    $applyButton.prop('disabled', true);
    
    // Simulate a small delay for better UX
    setTimeout(() => {
        if (enteredPassword === correctPassword) {
            // Correct password - open URL and close modal
            window.open(url, '_blank');
            hidePasswordModal();
        } else {
            // Incorrect password - show error
            $error.show();
            $input.addClass('error');
            $input.focus();
            
            // Shake animation
            $modal.find('.password-modal-content').addClass('shake');
            setTimeout(() => {
                $modal.find('.password-modal-content').removeClass('shake');
            }, 500);
        }
        
        // Reset button
        $applyButton.text('APPLY');
        $applyButton.prop('disabled', false);
    }, 500);
}

// Add CSS for body scroll lock and shake animation
$(document).ready(function(){
    // Add CSS to head if not already present
    if (!$('#password-modal-styles').length) {
        $('<style id="password-modal-styles">')
            .text(`
                body.modal-open {
                    overflow: hidden;
                }
                
                .password-input-container input.error {
                    border-color: #c33 !important;
                    background: #fee !important;
                }
                
                .password-modal-content.shake {
                    animation: shake 0.5s ease-in-out;
                }
                
                @keyframes shake {
                    0%, 100% { transform: scale(1) translateY(0) translateX(0); }
                    25% { transform: scale(1) translateY(0) translateX(-5px); }
                    75% { transform: scale(1) translateY(0) translateX(5px); }
                }
            `)
            .appendTo('head');
    }
});
