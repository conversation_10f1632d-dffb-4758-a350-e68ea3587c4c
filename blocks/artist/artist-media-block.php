<?php
// Add security headers for media embeds
add_action('wp_head', function() {
    if (is_singular('artist')) {
        echo '<meta http-equiv="Content-Security-Policy" content="frame-src https://www.youtube.com https://open.spotify.com; media-src https:; script-src \'self\' \'unsafe-inline\' https:;">' . "\n";
    }
});

$artist_id = $args['artist_id'] ?? get_the_ID();
$artist_name = get_the_title($artist_id);

// Get custom fields
$youtube_url = get_field('youtube_url', $artist_id);
$spotify_url = get_field('spotify_url', $artist_id);

// Extract YouTube video ID or playlist ID from URL
$youtube_video_id = '';
$youtube_playlist_id = '';
$youtube_embed_type = '';
if ($youtube_url) {
    $youtube_data = extract_youtube_data($youtube_url);
    $youtube_video_id = $youtube_data['video_id'];
    $youtube_playlist_id = $youtube_data['playlist_id'];
    $youtube_embed_type = $youtube_data['type'];
}

// Extract Spotify embed URL from URL
$spotify_embed_url = '';
if ($spotify_url) {
    $spotify_embed_url = convert_spotify_url_to_embed($spotify_url);
}

/**
 * Extract YouTube video ID or playlist ID from various YouTube URL formats
 */
function extract_youtube_data($url) {
    // Force HTTPS for security
    $url = str_replace('http://', 'https://', $url);

    // Log the URL being processed for debugging
    error_log('Processing YouTube URL: ' . $url);

    $result = [
        'video_id' => '',
        'playlist_id' => '',
        'type' => ''
    ];

    // Check for playlist URLs first
    if (preg_match('/[?&]list=([a-zA-Z0-9_-]+)/', $url, $matches)) {
        $result['playlist_id'] = $matches[1];
        $result['type'] = 'playlist';
        error_log('YouTube playlist ID extracted: ' . $matches[1]);
        return $result;
    }

    // Check for video URLs
    $video_patterns = [
        // Standard youtube.com/watch?v= format with optional parameters
        '/youtube\.com\/watch\?.*v=([a-zA-Z0-9_-]{11})/',
        // youtu.be/ format with optional parameters (like ?si=)
        '/youtu\.be\/([a-zA-Z0-9_-]{11})(?:\?.*)?/',
        // youtube.com/embed/ format
        '/youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/',
        // youtube.com/v/ format (legacy)
        '/youtube\.com\/v\/([a-zA-Z0-9_-]{11})/',
    ];

    foreach ($video_patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            $result['video_id'] = $matches[1];
            $result['type'] = 'video';
            error_log('YouTube video ID extracted: ' . $matches[1]);
            return $result;
        }
    }

    error_log('No YouTube video or playlist ID found in URL: ' . $url);
    return $result;
}

/**
 * Convert Spotify URL to embed URL
 */
function convert_spotify_url_to_embed($url) {
    // Remove query parameters and extract the important part
    $url = strtok($url, '?');

    // Force HTTPS for security
    $url = str_replace('http://', 'https://', $url);

    // Convert open.spotify.com to embed format
    if (strpos($url, 'open.spotify.com') !== false) {
        $embed_url = str_replace('open.spotify.com', 'open.spotify.com/embed', $url);
        return $embed_url . '?utm_source=generator&theme=0';
    }

    return '';
}


?>

<section class="artistMediaBlock" data-init data-show-cursor>
    <div class="contentWrapper smaller">
        <h2 class="normalTitle">PLAYLIST</h2>
        
        <div class="mediaContainer">
            <?php if ($youtube_video_id || $youtube_playlist_id): ?>
                <div class="youtubeContainer">
                    <div class="videoWrapper">
                        <?php if ($youtube_embed_type === 'playlist'): ?>
                            <iframe
                                src="https://www.youtube.com/embed/videoseries?list=<?= esc_attr($youtube_playlist_id) ?>&rel=0&showinfo=0&modestbranding=1&fs=0&autoplay=0&controls=1&disablekb=0&enablejsapi=0&iv_load_policy=3&loop=0&origin=<?= esc_url(home_url()) ?>"
                                frameborder="0"
                                allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                allowfullscreen
                                loading="lazy"
                                title="<?= esc_attr($artist_name) ?> - YouTube Playlist"
                                referrerpolicy="strict-origin-when-cross-origin">
                            </iframe>
                        <?php else: ?>
                            <iframe
                                src="https://www.youtube.com/embed/<?= esc_attr($youtube_video_id) ?>?rel=0&showinfo=0&modestbranding=1&fs=0&autoplay=0&controls=1&disablekb=0&enablejsapi=0&iv_load_policy=3&loop=0&origin=<?= esc_url(home_url()) ?>"
                                frameborder="0"
                                allow="accelerometer; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                                allowfullscreen
                                loading="lazy"
                                title="<?= esc_attr($artist_name) ?> - YouTube Video"
                                referrerpolicy="strict-origin-when-cross-origin">
                            </iframe>
                        <?php endif; ?>

                        <!-- Fallback link for cases where iframe truly fails -->
                        <noscript>
                            <div class="youtube-fallback">
                                <div class="fallback-message">
                                    <p>Watch on YouTube</p>
                                    <p><small>JavaScript is required to view embedded content</small></p>
                                    <?php if ($youtube_embed_type === 'playlist'): ?>
                                        <a href="https://www.youtube.com/playlist?list=<?= esc_attr($youtube_playlist_id) ?>" target="_blank" class="button">
                                            <span class="innerText">Open Playlist on YouTube</span>
                                            <span class="arrows">
                                                <i class="icon-arrow-right-up"></i>
                                                <i class="icon-arrow-right-up"></i>
                                            </span>
                                        </a>
                                    <?php else: ?>
                                        <a href="https://www.youtube.com/watch?v=<?= esc_attr($youtube_video_id) ?>" target="_blank" class="button">
                                            <span class="innerText">Open Video on YouTube</span>
                                            <span class="arrows">
                                                <i class="icon-arrow-right-up"></i>
                                                <i class="icon-arrow-right-up"></i>
                                            </span>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </noscript>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($spotify_embed_url): ?>
                <div class="spotifyContainer">
                    <iframe
                        src="<?= esc_url($spotify_embed_url) ?>"
                        width="100%"
                        height="100%"
                        frameborder="0"
                        allowfullscreen=""
                        allow="clipboard-write; encrypted-media; fullscreen; picture-in-picture"
                        referrerpolicy="no-referrer-when-downgrade"
                        loading="lazy">
                    </iframe>

                    <!-- Fallback link if iframe fails -->
                    <noscript>
                        <div class="spotify-fallback">
                            <a href="<?= esc_url($spotify_url) ?>" target="_blank" class="button">
                                <span class="innerText">Open on Spotify</span>
                                <span class="arrows">
                                    <i class="icon-arrow-right-up"></i>
                                    <i class="icon-arrow-right-up"></i>
                                </span>
                            </a>
                        </div>
                    </noscript>
                </div>
            <?php endif; ?>
        </div>

        <?php if (!$youtube_video_id && !$youtube_playlist_id && !$spotify_embed_url && current_user_can('edit_posts')): ?>
            <div class="noMediaMessage">
                <p>No media configured for this artist.</p>
                <p><small>Add YouTube URL (video or playlist) and Spotify URL in the artist edit page.</small></p>

                <?php if (WP_DEBUG && ($youtube_url || $spotify_url)): ?>
                    <div style="margin-top: 20px; padding: 10px; background: #f0f0f0; border: 1px solid #ccc; font-family: monospace; font-size: 12px;">
                        <strong>Debug Info:</strong><br>
                        <?php if ($youtube_url): ?>
                            YouTube URL: <?= esc_html($youtube_url) ?><br>
                            Type: <?= esc_html($youtube_embed_type ?: 'NONE') ?><br>
                            Video ID: <?= esc_html($youtube_video_id ?: 'NONE') ?><br>
                            Playlist ID: <?= esc_html($youtube_playlist_id ?: 'NONE') ?><br>
                        <?php endif; ?>
                        <?php if ($spotify_url): ?>
                            Spotify URL: <?= esc_html($spotify_url) ?><br>
                            Embed URL: <?= esc_html($spotify_embed_url ?: 'NONE') ?><br>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</section>
